import React from 'react';

interface LogoProps {
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

const Logo: React.FC<LogoProps> = ({ size = 'md', className = '' }) => {
  const sizeClasses = {
    sm: 'w-8 h-8 text-sm',
    md: 'w-10 h-10 text-lg',
    lg: 'w-12 h-12 text-xl'
  };

  return (
    <div 
      className={`${sizeClasses[size]} ${className} bg-black text-white flex items-center justify-center font-bold rounded-lg`}
      style={{
        display: 'inline-flex',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: 'var(--color-primary)',
        color: 'var(--color-secondary)',
        fontWeight: '700',
        borderRadius: 'var(--radius-lg)',
        width: size === 'sm' ? '2rem' : size === 'md' ? '2.5rem' : '3rem',
        height: size === 'sm' ? '2rem' : size === 'md' ? '2.5rem' : '3rem',
        fontSize: size === 'sm' ? 'var(--font-size-sm)' : size === 'md' ? 'var(--font-size-lg)' : 'var(--font-size-xl)'
      }}
    >
      P
    </div>
  );
};

export default Logo;
