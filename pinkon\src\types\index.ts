export interface Feature {
  id: string;
  title: string;
  description: string;
  icon: string;
  category: 'image' | 'video' | 'file' | 'ai';
}

export interface PricingPlan {
  id: string;
  name: string;
  price: number;
  period: string;
  attempts: number;
  features: string[];
  popular?: boolean;
}

export interface NavigationItem {
  label: string;
  href: string;
}

export interface HeroSection {
  title: string;
  subtitle: string;
  description: string;
  primaryCTA: {
    text: string;
    href: string;
  };
  secondaryCTA: {
    text: string;
    href: string;
  };
}

export interface FeatureCategory {
  id: string;
  title: string;
  description: string;
  features: Feature[];
}
